import { createApi } from "@reduxjs/toolkit/query/react";
import type {
  FetchBaseQueryError,
  QueryReturnValue,
} from "@reduxjs/toolkit/query";
import type { RecentActivityParams, RecentActivityResponse } from "@/types/recentActivity";
import config from "@/config";
import { baseQueryWithReauth } from "./interceptorsSlice";
import { mockRecentActivityResponse } from "./mocks/recentActivity.mock";

export const recentActivityApiSlice = createApi({
  reducerPath: "recentActivityApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getRecentActivity: builder.query<RecentActivityResponse, RecentActivityParams, void>({
      async queryFn(
        arg = {},
        _queryApi,
        _extraOptions,
        baseQuery,
      ): Promise<
        QueryReturnValue<
          RecentActivityResponse,
          FetchBaseQueryError,
          {} | undefined
        >
      > {
        if (config.featureFlags.RECENT_ACTIVITY_API_USE_MOCK) {

          const mockData = mockRecentActivityResponse;
          const { pageNumber = 1, pageSize = 20 } = arg;
          const startIndex = (pageNumber - 1) * pageSize;
          const endIndex = startIndex + pageSize;

           return {
              data: {
                records: mockData.records.slice(startIndex, endIndex),
                pageCount: Math.ceil(mockData.records.length / pageSize),
                pageNumber,
                pageSize,
                totalRecordCount: mockData.records.length,
              },
          };
        }

       const params = new URLSearchParams();
        if (arg.pageNumber) params.append('pageNumber', arg.pageNumber.toString());
        if (arg.pageSize) params.append('pageSize', arg.pageSize.toString());
        if (arg.sortField) params.append('sortField', arg.sortField);
        if (arg.sortDirection) params.append('sortDirection', arg.sortDirection);

        const result = await baseQuery({
          headers: {
            Authorization: "Bearer " + "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
          },
          url: `/activity-logs?${params.toString()}`,
          method: "GET",
        });

        return result as QueryReturnValue<
          RecentActivityResponse,
          FetchBaseQueryError,
          {} | undefined
        >;
      },
    }),
  }),
});

export const { useGetRecentActivityQuery } = recentActivityApiSlice;
