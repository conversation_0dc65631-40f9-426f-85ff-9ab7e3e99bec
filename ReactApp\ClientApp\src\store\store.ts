import { configureStore } from "@reduxjs/toolkit";
import type { ThunkAction, Action } from "@reduxjs/toolkit";
import appReducer from "@/appSlice";
import { sampleApiSlice } from "@/api/sampleApiSlice";
import { recentActivityApiSlice } from "@/api/recentActivityApiSlice";
import { todoApiSlice } from "@/api/todoApiSlice";
import { termsApiSlice } from "@/api/termsApiSlice";
import { auditTrailsApiSlice } from "@/api/auditTrailsApiSlice";

// add slices here
export const store = configureStore({
  reducer: {
    app: appReducer,
    [sampleApiSlice.reducerPath]: sampleApiSlice.reducer,
    [todoApiSlice.reducerPath]: todoApiSlice.reducer,
    [termsApiSlice.reducerPath]: termsApiSlice.reducer,
    [auditTrailsApiSlice.reducerPath]: auditTrailsApiSlice.reducer,
    [recentActivityApiSlice.reducerPath]: recentActivityApiSlice.reducer,
    // Add other slices here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(sampleApiSlice.middleware)
      .concat(todoApiSlice.middleware)
      .concat(termsApiSlice.middleware)
      .concat(auditTrailsApiSlice.middleware)
      .concat(recentActivityApiSlice.middleware),
  // add RTK Query middleware
});

// strong typing
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// async thunk actions
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
