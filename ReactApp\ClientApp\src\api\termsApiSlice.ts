import { createApi } from "@reduxjs/toolkit/query/react";
import type {
  FetchBaseQueryError,
  QueryReturnValue,
} from "@reduxjs/toolkit/query";
import type { Terms } from "@/types/terms";
import config from "@/config";
import { baseQueryWithReauth } from "./interceptorsSlice";
import { mockTermsData } from "./mocks/terms.mock";

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
const db = config.temporary.db || "NTB1";
const authorization = config.temporary.authorization;

export const termsApiSlice = createApi({
  reducerPath: "termsApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getLatest: builder.query<Terms, void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        if (config.featureFlags.api.GET_LATEST_TERMS) {
          await wait(1000);
          return { data: mockTermsData, meta: {} };
        }

        const result = await baseQuery({
          url: `${db}/api/terms-and-conditions/latest`,
          method: "GET",
          headers: {
            Accept: "text/plain",
            Authorization: authorization,
          },
        });

        return result as QueryReturnValue<Terms, FetchBaseQueryError, {}>;
      },
    }),

    saveTerms: builder.mutation<void, Partial<Terms> & { file?: File }>({
      queryFn: async (
        body,
        _queryApi,
        _extraOptions,
        _baseQuery,
      ): Promise<QueryReturnValue<void, FetchBaseQueryError, {}>> => {
        if (config.featureFlags.api.SAVE_TERMS) {
          await wait(1000);
          return { data: undefined, meta: {} };
        }

        const formData = new FormData();

        if (body?.file instanceof File && body.file.size > 0) {
          formData.append("File", body.file);
        }

        if (body?.statementOfAgreement?.trim()) {
          formData.append(
            "StatementOfAgreement",
            body.statementOfAgreement.trim(),
          );
        }

        const tp = body?.triggerPoints;
        if (tp) {
          formData.append(
            "TriggerPoints.PromptAfterFirstLogin",
            String(tp.promptAfterFirstLogin),
          );
          formData.append(
            "TriggerPoints.PromptAnnually",
            String(tp.promptAnnually),
          );
          formData.append(
            "TriggerPoints.PromptQuarterly",
            String(tp.promptQuarterly),
          );
          formData.append(
            "TriggerPoints.PromptWhenUpdated",
            String(tp.promptWhenUpdated),
          );
        }

        try {
          const res = await fetch(
            `${config.baseUrl}/${db}/api/terms-and-conditions/upload`,
            {
              method: "POST",
              headers: {
                Accept: "*/*",
                Authorization: authorization,
              },
              body: formData,
            },
          );

          if (!res.ok) {
            const errorText = await res.text();
            return {
              error: {
                status: res.status,
                data: errorText,
              },
              meta: {},
            };
          }

          return { data: undefined, meta: {} };
        } catch (error: any) {
          return {
            error: {
              status: "FETCH_ERROR",
              data: undefined,
              error: error?.message ?? "Unknown error",
            },
            meta: {},
          };
        }
      },
    }),

    updateTerms: builder.mutation<
      { success: boolean; message: string },
      {
        termsAndConditionsId: number;
        statementOfAgreement: string;
        triggerPoints: {
          promptAfterFirstLogin: boolean;
          promptAnnually: boolean;
          promptQuarterly: boolean;
          promptWhenUpdated: boolean;
        };
      }
    >({
      queryFn: async (body, _api, _extra, baseQuery) => {
        if (config.featureFlags.api.UPDATE_TERMS) {
          await wait(1000);
          return {
            data: {
              success: true,
              message: "Terms & Conditions updated successfully.",
            },
            meta: {},
          };
        }

        const { termsAndConditionsId, ...rest } = body;

        const result = await baseQuery({
          url: `${db}/api/terms-and-conditions/${termsAndConditionsId}`,
          method: "PUT",
          headers: {
            Accept: "*/*",
            Authorization: authorization,
            "Content-Type": "application/json-patch+json",
          },
          body: rest,
        });

        return result as QueryReturnValue<
          { success: boolean; message: string },
          FetchBaseQueryError,
          {}
        >;
      },
    }),
  }),
});

export const {
  useGetLatestQuery,
  useSaveTermsMutation,
  useUpdateTermsMutation,
} = termsApiSlice;
