import type { GridCustomFilterCellProps } from "@progress/kendo-react-grid";
import { HeaderTdElement } from "@progress/kendo-react-data-tools";
import FileSizeFilter from "./FileSizeFilter";
import DateRangeFilter from "./DateRangeFilter";
import type { FC } from "react";

const FileSizeFilterCell = (props: GridCustomFilterCellProps) => (
  <HeaderTdElement columnId={props.thProps?.columnId || ""} {...props.thProps}>
    <FileSizeFilter {...props} />
  </HeaderTdElement>
);

const DateRangeFilterCell = (props: GridCustomFilterCellProps) => (
  <HeaderTdElement columnId={props.thProps?.columnId || ""} {...props.thProps}>
    <DateRangeFilter {...props} />
  </HeaderTdElement>
);

export const auditTrailFilterCellMap: Record<
  string,
  FC<GridCustomFilterCellProps>
> = {
  fileSize: FileSizeFilterCell,
  uploadedOn: DateRangeFilterCell,
};
