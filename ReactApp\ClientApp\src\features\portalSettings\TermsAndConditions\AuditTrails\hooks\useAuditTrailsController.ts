import { useState, useCallback } from "react";
import { useAuditTrails } from "./useGetAuditTrails";
import { useGetAuditTrailsColumns } from "./useGetAuditTrailsColumns";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";
import type {
  GridPageChangeEvent,
  GridFilterChangeEvent,
  GridSortChangeEvent,
} from "@progress/kendo-react-grid";

export const useAuditTrailsController = () => {
  const [skip, setSkip] = useState(0);
  const [take, setTake] = useState(10);
  const [filters, setFilters] = useState<CompositeFilterDescriptor>({
    logic: "and",
    filters: [],
  });
  const [sorts, setSorts] = useState<SortDescriptor[]>([]);

  const { auditTrailsColumns, isLoading: isColumnsLoading } =
    useGetAuditTrailsColumns();

  const {
    auditTrails,
    totalRecordCount,
    isLoading: isDataLoading,
    isError,
    error,
  } = useAuditTrails({
    skip,
    take,
    filters,
    sorts,
  });

  const handlePageChange = useCallback((event: GridPageChangeEvent) => {
    setSkip(event.page.skip);
    setTake(event.page.take);
  }, []);

  const handleSortChange = useCallback((event: GridSortChangeEvent) => {
    setSorts(event.sort);
    setSkip(0);
  }, []);

  const handleFilterChange = useCallback((event: GridFilterChangeEvent) => {
    setFilters(event.filter);
    setSkip(0);
  }, []);

  const handleRefresh = useCallback(() => {
    setFilters({ logic: "and", filters: [] });
    setSorts([]);
    setSkip(0);
    setTake(10);
  }, []);

  return {
    auditTrails,
    totalRecordCount,
    auditTrailsColumns,
    isColumnsLoading,
    isDataLoading,
    isError,
    error,
    pagination: { skip, take },
    filters,
    sorts,
    handlePageChange,
    handleSortChange,
    handleFilterChange,
    handleRefresh,
  };
};
