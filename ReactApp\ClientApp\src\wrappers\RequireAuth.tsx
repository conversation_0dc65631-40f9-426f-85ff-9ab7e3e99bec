import React from "react";
import { useOktaAuth } from "@okta/okta-react";
import { Navigate, useLocation } from "react-router-dom";

interface RequireAuthProps {
  children: React.ReactElement;
}

/**
 *  secure route wrapper.
 * - If user is authenticated → render protected content
 * - If not → redirect to /login
 */
export default function RequireAuth({ children }: RequireAuthProps) {
  const { authState } = useOktaAuth();
  const location = useLocation();

  if (!authState?.isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return children;
}
