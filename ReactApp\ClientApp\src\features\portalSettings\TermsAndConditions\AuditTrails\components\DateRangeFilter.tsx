import {
  DateRangePicker,
  type DateRangePickerChangeEvent,
} from "@progress/kendo-react-dateinputs";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import "../AuditTrails.scss";

const DateRangeFilter = (props: any) => {
  const { value = null, onChange } = props;

  const handleChange = (event: DateRangePickerChangeEvent) => {
    const start = event.value?.start;
    const end = event.value?.end;

    onChange({
      value: {
        start: start ? start.toISOString() : null,
        end: end ? end.toISOString() : null,
      },
      operator: "inRange",
      syntheticEvent: event.syntheticEvent,
    });
  };

  const handleClear = (event: any) => {
    event.preventDefault();
    onChange({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
  };

  const startDate = value?.start ? new Date(value.start) : null;
  const endDate = value?.end ? new Date(value.end) : null;

  return (
    <div className="fileSizeFilterContainer">
      <DateRangePicker
        format="dd/MM/yy"
        value={{
          start: startDate,
          end: endDate,
        }}
        min={startDate || undefined}
        onChange={handleChange}
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        type="button"
        onClick={handleClear}
        disabled={!value}
      />
    </div>
  );
};

export default DateRangeFilter;
