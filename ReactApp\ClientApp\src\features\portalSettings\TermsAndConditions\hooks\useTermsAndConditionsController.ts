import { useState, useMemo, useEffect } from "react";
import { debounce } from "lodash";
import { useNavigate } from "react-router-dom";
import { useGetLatest } from "./useGetLatest";
import { useSaveTerms } from "./useSaveTerms";
import { useUpdateTerms } from "./useUpdateTerms";
import type { Terms } from "@/types/terms";
import logger from "@/utils/logger";
import { fetchTermsPdfBlob } from "../utils/fetchTermsPdfBlob";

export function useTermsAndConditionsController() {
  const { terms, isLoading, isFetching, refetch } = useGetLatest();
  const { save, isSaving } = useSaveTerms();
  const { update, isUpdating } = useUpdateTerms();
  const navigate = useNavigate();

  const [previewStatement, setPreviewStatement] = useState<string | null>(null);
  const [previewBlobUrl, setPreviewBlobUrl] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [showAlert, setShowAlert] = useState(true);
  const [uploadedFileName, setUploadedFileName] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [termsData, setTermsData] = useState<Terms | null>(null);
  const [remoteBlobUrl, setRemoteBlobUrl] = useState<string | null>(null);
  const [statementInput, setStatementInput] = useState<string>("");

  const [triggerPoints, setTriggerPoints] = useState({
    promptAfterFirstLogin: false,
    promptWhenUpdated: false,
    promptAnnually: false,
    promptQuarterly: false,
  });

  const handleUpload = useMemo(
    () =>
      debounce((event: any) => {
        const fileInfo = event.affectedFiles?.[0];
        const file = fileInfo?.getRawFile?.() || fileInfo?.rawFile;

        if (file instanceof File) {
          setUploadedFileName(file.name);
          setSelectedFile(file);
        } else {
          logger.error("Uploaded item is not a valid File object", fileInfo);
          setErrorMessage("Failed to read uploaded file. Please try again.");
        }
      }, 300),
    [],
  );

  useEffect(() => {
    if (terms?.triggerPoints) {
      setTriggerPoints({
        promptAfterFirstLogin: !!terms.triggerPoints.promptAfterFirstLogin,
        promptWhenUpdated: !!terms.triggerPoints.promptWhenUpdated,
        promptAnnually: !!terms.triggerPoints.promptAnnually,
        promptQuarterly: !!terms.triggerPoints.promptQuarterly,
      });
    }

    if (terms?.statementOfAgreement) {
      setStatementInput(terms.statementOfAgreement);
    }
  }, [terms]);

  useEffect(() => {
    if (terms) {
      setTermsData(terms);
    }
  }, [terms]);

  useEffect(() => {
    let blobUrl: string | null = null;

    const loadPdf = async () => {
      if (terms?.termsAndConditionsId && !selectedFile) {
        try {
          const blob = await fetchTermsPdfBlob(terms.termsAndConditionsId);
          blobUrl = URL.createObjectURL(blob);
          setRemoteBlobUrl(blobUrl);
        } catch (err: unknown) {
          const error = err instanceof Error ? err : new Error(String(err));
          logger.error("Failed to download PDF blob", {
            message: error.message,
            stack: error.stack,
          });
        }
      } else {
        logger.warn(
          "Skipped PDF fetch. Either no termsId or selectedFile exists.",
        );
      }
    };

    loadPdf();

    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [terms, selectedFile]);

  useEffect(() => {
    return () => {
      handleUpload.cancel();
    };
  }, [handleUpload]);

  useEffect(() => {
    return () => {
      if (previewBlobUrl) {
        URL.revokeObjectURL(previewBlobUrl);
      }
    };
  }, [previewBlobUrl]);

  const handleRemoveFile = () => {
    setUploadedFileName("");
    setSelectedFile(null);
  };

  const handleSaveTerms = async () => {
    const currentStatement = statementInput.trim();
    const originalStatement = terms?.statementOfAgreement?.trim() || "";

    const payload = {
      termsAndConditionsId: terms?.termsAndConditionsId ?? 0,
      statementOfAgreement: currentStatement,
      triggerPoints: {
        promptAfterFirstLogin: triggerPoints.promptAfterFirstLogin,
        promptWhenUpdated: triggerPoints.promptWhenUpdated,
        promptAnnually: triggerPoints.promptAnnually,
        promptQuarterly: triggerPoints.promptQuarterly,
      },
    };

    const hasChanges =
      currentStatement !== originalStatement ||
      JSON.stringify(payload.triggerPoints) !==
        JSON.stringify({
          promptAfterFirstLogin: terms?.triggerPoints.promptAfterFirstLogin,
          promptWhenUpdated: terms?.triggerPoints.promptWhenUpdated,
          promptAnnually: terms?.triggerPoints.promptAnnually,
          promptQuarterly: terms?.triggerPoints.promptQuarterly,
        });

    if (previewBlobUrl && selectedFile instanceof File) {
      await save(
        {
          ...payload,
          file: selectedFile,
        },
        (message) => setErrorMessage(message),
        () => setSuccessMessage("Terms saved successfully."),
      );
    } else if (hasChanges) {
      await update(
        payload,
        (message) => setErrorMessage(message),
        (msg) => setSuccessMessage(msg || "Terms updated successfully."),
      );
    } else {
      setErrorMessage("No changes to save.");
    }
  };

  const handleGeneratePreview = async () => {
    const originalStatement = terms?.statementOfAgreement?.trim() || "";
    const currentStatement = statementInput.trim();

    if (!selectedFile && !termsData && originalStatement === currentStatement) {
      setErrorMessage("Missing file or no changes detected");
      return;
    }

    logger.info("Generating preview with:", {
      file: selectedFile,
      statementOfAgreement: currentStatement,
      triggerPoints,
    });

    if (selectedFile && selectedFile instanceof Blob) {
      const blobUrl = URL.createObjectURL(selectedFile);
      setPreviewBlobUrl(blobUrl);
    }

    setPreviewStatement(currentStatement);
    setShowAlert(true);
    setErrorMessage("");
  };

  const isGenerateDisabled =
    !selectedFile &&
    !termsData &&
    statementInput.trim() === (terms?.statementOfAgreement?.trim() || "");

  const resetForm = async () => {
    try {
      const { data } = await refetch();
      if (data) {
        setStatementInput(data.statementOfAgreement || "");
        setTriggerPoints({
          promptAfterFirstLogin: !!data.triggerPoints.promptAfterFirstLogin,
          promptWhenUpdated: !!data.triggerPoints.promptWhenUpdated,
          promptAnnually: !!data.triggerPoints.promptAnnually,
          promptQuarterly: !!data.triggerPoints.promptQuarterly,
        });
        setTermsData(data);
        setErrorMessage("");
        setSuccessMessage("");
        setUploadedFileName("");
        setSelectedFile(null);
        setPreviewBlobUrl(null);
        setPreviewStatement(null);
      }
    } catch (error) {
      logger.error("Reset failed", error as any);
      setErrorMessage("Failed to reset form.");
    }
  };

  const hasUnsavedChanges = () => {
    if (!terms) return false;

    const hasStatementChanged =
      statementInput.trim() !== (terms.statementOfAgreement?.trim() ?? "");

    const hasTriggerPointsChanged =
      terms.triggerPoints?.promptAfterFirstLogin !==
        triggerPoints.promptAfterFirstLogin ||
      terms.triggerPoints?.promptAnnually !== triggerPoints.promptAnnually ||
      terms.triggerPoints?.promptQuarterly !== triggerPoints.promptQuarterly ||
      terms.triggerPoints?.promptWhenUpdated !==
        triggerPoints.promptWhenUpdated;

    const hasFileChanged = selectedFile !== null;

    return hasStatementChanged || hasTriggerPointsChanged || hasFileChanged;
  };

  return {
    terms,
    isLoading,
    isFetching,
    isSaving: isSaving || isUpdating,
    errorMessage,
    setErrorMessage,
    successMessage,
    setSuccessMessage,
    showAlert,
    setShowAlert,
    uploadedFileName,
    setUploadedFileName,
    selectedFile,
    setSelectedFile,
    triggerPoints,
    setTriggerPoints,
    handleUpload,
    handleRemoveFile,
    handleSaveTerms,
    handleGeneratePreview,
    resetForm,
    navigate,
    termsData,
    setTermsData,
    remoteBlobUrl,
    previewStatement,
    previewBlobUrl,
    statementInput,
    setStatementInput,
    isGenerateDisabled,
    hasUnsavedChanges,
  };
}
