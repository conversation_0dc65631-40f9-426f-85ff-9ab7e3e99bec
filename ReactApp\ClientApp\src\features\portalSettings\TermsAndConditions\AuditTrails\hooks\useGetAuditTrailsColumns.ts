import { useEffect, useState } from "react";
import { useGetAuditTrailsColumnsQuery } from "@/api/auditTrailsApiSlice";
import type { AuditTrailsColumn } from "@/types/auditTrails";

export const useGetAuditTrailsColumns = () => {
  const { data, isLoading } = useGetAuditTrailsColumnsQuery();
  const [auditTrailsColumns, setAuditTrailsColumns] = useState<
    AuditTrailsColumn[]
  >([]);

  useEffect(() => {
    if (!isLoading && data) {
      setAuditTrailsColumns(data);
    }
  }, [data, isLoading]);

  return {
    auditTrailsColumns,
    isLoading,
  };
};
