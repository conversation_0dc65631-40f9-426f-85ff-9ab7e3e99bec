import { Button } from "@progress/kendo-react-buttons";
import type { AuditTrail } from "@/types/auditTrails";

interface ExportCsvCellProps {
  dataItem: AuditTrail;
}

export default function ExportCsvCell({ dataItem }: ExportCsvCellProps) {
  const handleExportCsv = () => {
    const headers = Object.keys(dataItem).join(",");
    const values = Object.values(dataItem)
      .map((val) => {
        const stringVal = String(val);
        return stringVal.includes(",") ||
          stringVal.includes("\n") ||
          stringVal.includes('"')
          ? `"${stringVal.replace(/"/g, '""')}"`
          : stringVal;
      })
      .join(",");

    const csvContent = `${headers}\n${values}`;
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = `${dataItem.fileName || "data"}_${dataItem.id}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <td className="k-command-cell">
      <Button onClick={handleExportCsv} themeColor="primary" fillMode="flat">
        Export to CSV
      </Button>
    </td>
  );
}
