import { mockColumns, mockData } from "@/api/mocks/auditTrailMock";
import type {
  AuditTrailsColumn,
  AuditTrailsResponse,
} from "@/types/auditTrails";
import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";

import type {
  SortDescriptor,
  CompositeFilterDescriptor,
} from "@progress/kendo-data-query";
import { filterBy, orderBy } from "@progress/kendo-data-query";

interface GetAuditTrailsParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

export const auditTrailsApiSlice = createApi({
  reducerPath: "auditTrailsApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getAuditTrailsColumns: builder.query<AuditTrailsColumn[], void>({
      queryFn: async () => {
        await new Promise((resolve) => setTimeout(resolve, 500));
        return { data: mockColumns };
      },
    }),
    getAuditTrails: builder.query<AuditTrailsResponse, GetAuditTrailsParams>({
      queryFn: async ({ skip, take, filters, sorts }) => {
        await new Promise((resolve) => setTimeout(resolve, 500));
        let currentData = mockData.records;

        if (filters && filters.filters && filters.filters.length > 0) {
          filters.filters.forEach((f) => {
            if (
              "operator" in f &&
              f.operator === "inRange" &&
              typeof f.value === "object"
            ) {
              let { start, end } = f.value;

              const min = start ? new Date(start) : null;
              const max = end ? new Date(end) : null;

              if (typeof f.field === "string") {
                currentData = currentData.filter((item) => {
                  const currentRaw = item[f.field as keyof typeof item];
                  const current = new Date(currentRaw);
                  return (
                    (min == null || current >= min) &&
                    (max == null || current <= max)
                  );
                });
              }
            } else {
              currentData = filterBy(currentData, {
                logic: "and",
                filters: [f],
              });
            }
          });
        }

        if (sorts && sorts.length > 0) {
          currentData = orderBy(currentData, sorts);
        }

        const totalRecordCount = currentData.length;
        const pageNumber = Math.floor(skip / take) + 1;
        const pageCount = Math.ceil(totalRecordCount / take);
        const records = currentData.slice(skip, skip + take);

        return {
          data: {
            records,
            totalRecordCount,
            pageCount,
            pageNumber,
            pageSize: take,
          },
        };
      },
    }),
  }),
});

export const { useGetAuditTrailsColumnsQuery, useGetAuditTrailsQuery } =
  auditTrailsApiSlice;
