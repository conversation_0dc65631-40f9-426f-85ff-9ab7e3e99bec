import {
  NumericTextBox,
  type NumericTextBoxChangeEvent,
  type NumericTextBoxHandle,
} from "@progress/kendo-react-inputs";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import "../AuditTrails.scss";

const FileSizeFilter = (props: any) => {
  let minTextBox: NumericTextBoxHandle | null;
  let maxTextBox: NumericTextBoxHandle | null;

  const onChange = (event: NumericTextBoxChangeEvent) => {
    props.onChange({
      value: { min: minTextBox?.value, max: maxTextBox?.value },
      operator: "inRange",
      syntheticEvent: event.syntheticEvent,
    });
  };

  const onClearButtonClick = (event: { preventDefault: () => void }) => {
    event.preventDefault();
    props.onChange({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
  };

  const value = props.value || null;

  return (
    <div className="fileSizeFilterContainer">
      <NumericTextBox
        value={value && value.min}
        ref={(numeric) => {
          minTextBox = numeric;
        }}
        placeholder="Min"
        onChange={onChange}
        min={0}
        width={75}
      />
      <NumericTextBox
        value={value && value.max}
        ref={(numeric) => {
          maxTextBox = numeric;
        }}
        placeholder="Max"
        onChange={onChange}
        min={0}
        width={75}
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        disabled={!value}
        onClick={onClearButtonClick}
        type="button"
      ></Button>
    </div>
  );
};

export default FileSizeFilter;
