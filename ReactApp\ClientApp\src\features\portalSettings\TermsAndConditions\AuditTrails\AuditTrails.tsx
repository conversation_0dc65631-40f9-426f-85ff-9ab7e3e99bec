import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { AuditTrailsGrid } from "./components";
import { useAuditTrailsController } from "./hooks/useAuditTrailsController";

import "./AuditTrails.scss";

export default function AuditTrails() {
  const {
    auditTrails,
    totalRecordCount,
    auditTrailsColumns,
    isDataLoading,
    isColumnsLoading,
    pagination,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAuditTrailsController();

  return (
    <SectionLayout>
      <AuditTrailsGrid
        auditTrails={auditTrails}
        totalRecordCount={totalRecordCount}
        auditTrailsColumns={auditTrailsColumns}
        isLoading={isDataLoading}
        isColumnsLoading={isColumnsLoading}
        skip={pagination.skip}
        take={pagination.take}
        filters={filters}
        sorts={sorts}
        onPageChange={handlePageChange}
        onFilterChange={handleFilterChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
      />
    </SectionLayout>
  );
}
