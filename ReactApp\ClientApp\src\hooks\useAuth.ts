import { useOktaAuth } from "@okta/okta-react";

export function useAuth() {
  const { authState, oktaAuth } = useOktaAuth();

  const login = () => oktaAuth.signInWithRedirect();
  const logout = () => oktaAuth.signOut();

  const user = authState?.isAuthenticated ? authState?.idToken?.claims : null;

  return {
    authState,
    isAuthenticated: !!authState?.isAuthenticated,
    login,
    logout,
    oktaAuth,
    user,
  };
}
