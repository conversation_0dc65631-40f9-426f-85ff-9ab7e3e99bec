/* Layout */
.content-left,
.content-right {
  display: flex;
  flex-direction: column;
  gap: 8px;

  min-width: 300px;
  max-width: 600px;
  width: 100%;
  flex: 1 1 auto;
  height: 100%;
  overflow: hidden;

  @media (min-width: 1440px) {
    max-width: 700px;
  }

  @media (min-width: 1920px) {
    max-width: 800px;
  }
}

.section-header {
  margin: 0;
  background-color: var(--kendo-component-bg, #f5f6fa);
  padding: 5px 15px;
  border-bottom: 1px solid var(--kendo-border-color, #e0e0e0);
  font-weight: 600;
  font-size: var(--kendo-font-size-sm, 12px);
}

.section-inner {
  flex-grow: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Header Action Buttons */
.header-action-btn {
  height: var(--kendo-button-height-sm, 1.75rem);
  font-size: var(--kendo-font-size-sm, 12px);
}

/* Alert Box */
.alert-box {
  display: flex;
  align-items: center;
  border: 1px solid var(--kendo-border-color, #ccc);
  background-color: var(--kendo-component-bg, #e6f4ff);
  padding: 6px;
  gap: 12px;
  border-radius: 2px;
}

.alert-icon {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 1px solid var(--kendo-body-text, #333);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  line-height: 1;
}

.alert-message {
  font-size: var(--kendo-font-size-sm, 12px);
  flex-grow: 1;
}

/* Upload Section */
.upload-wrapper {
  padding: 24px;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-label {
  font-weight: bold;
  font-size: var(--kendo-font-size-sm, 12px);
}

.upload-input-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-helper-text {
  font-size: var(--kendo-font-size-sm, 12px);
  color: var(--kendo-subtle-text, #666);
}

/* Conditions Form */
.conditions-wrapper {
  padding: 16px;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.conditions-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.conditions-header {
  display: flex;
  justify-content: space-between;
  font-size: var(--kendo-font-size-sm, 12px);
  font-weight: bold;
}

.conditions-subtext {
  font-size: var(--kendo-font-size-sm, 12px);
  color: var(--kendo-subtle-text, #666);
}

.conditions-options {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--kendo-font-size-sm, 12px);
}

.option-label {
  font-size: var(--kendo-font-size-sm, 12px);
}

/* Statement Editor */
.statement-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.statement-wrapper {
  font-weight: bold;
  font-size: var(--kendo-font-size-sm, 12px);
}

.statement-card {
  padding: 16px;
  border: 1px solid var(--kendo-border-color, #e0e0e0);
}

.statement-helper {
  font-size: 13px;
  color: var(--kendo-subtle-text, #666);
}

/* PDF Preview Panel */
.pdf-preview-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-grow: 1;
}

.pdf-viewer {
  width: 100%;
  min-height: 300px;
  max-height: 350px;
  overflow: auto;
  border: 1px solid var(--kendo-border-color, #e0e0e0);

  // Center fallback only if no PDF
  display: flex;
  align-items: center;
  justify-content: center;

  // Responsive adjustments
  @media (min-height: 900px) {
    min-height: 450px;
    max-height: 500px;
  }

  .no-preview {
    color: #888;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
  }
}


/* PDFViewer Toolbar */
.k-pdf-viewer .k-toolbar {
  justify-content: center !important;
  display: flex !important;
}

/* PDF Content Scroll and Cursor */
.k-pdf-viewer-content {
  overflow: auto !important;
  cursor: grab;
}

.k-pdf-viewer-content:active {
  cursor: grabbing;
}

/* Viewport and Canvas container cleanup */
.k-pdf-viewer .k-pdf-viewer-viewport,
.k-pdf-viewer .k-canvas-container,
.k-pdf-viewer .k-pdf-viewer-pages {
  width: 100% !important;
  max-width: 100% !important;
  // overflow: auto !important;
  box-sizing: border-box;
}

/* Preview Section */
.preview-text {
  font-size: 13px;
  color: var(--kendo-body-text, #333);
  padding: 0 2px;
  border-top: 1px solid var(--kendo-border-color, #e0e0e0);
  border-bottom: 1px solid var(--kendo-border-color, #e0e0e0);
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 2px;
}

.preview-links {
  display: flex;
  gap: 16px;
  align-items: center;
}

.preview-link {
  display: flex;
  align-items: center;
  font-size: var(--kendo-font-size-sm, 12px);
  text-decoration: none;
  color: var(--kendo-body-text, #333);

  svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.cancel-link {
  text-decoration: underline;
  color: var(--kendo-link-text, #0078d4);
  font-size: var(--kendo-font-size-md, 14px);
}

.disabled-preview {
  pointer-events: none;
  opacity: 0.5;
  user-select: none;
}

/* Responsive Adjustments */
@media (max-height: 800px) {
  .dashboard-main {
    gap: 8px;
    margin-bottom: 8px;
  }

  .upload-wrapper,
  .conditions-wrapper,
  .statement-card {
    padding: 8px;
  }

  .section-inner {
    padding: 4px;
    gap: 4px;
  }

  .section-header {
    padding: 4px 10px;
    font-size: calc(var(--kendo-font-size-sm, 12px) - 1px);
  }

  .alert-message,
  .upload-label,
  .option-label,
  .statement-wrapper,
  .statement-helper,
  .preview-text,
  .preview-link span {
    font-size: calc(var(--kendo-font-size-sm, 12px) - 1px);
  }

  .preview-footer {
    padding: 2px 0;
  }

  .cancel-link {
    font-size: calc(var(--kendo-font-size-sm, 12px) - 1px);
  }
}