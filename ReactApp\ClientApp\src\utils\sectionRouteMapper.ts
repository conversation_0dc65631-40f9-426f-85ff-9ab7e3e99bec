import { ROUTES } from "@/constants/routes";

export const sectionRouteMap: Record<string, string> = {
  // Portal Settings
  "Terms & Conditions": ROUTES.TERMS_AND_CONDITIONS,
  "Portal Role Assignment": ROUTES.PORTAL_ROLE_ASSIGNMENT,
  "Folder Permissions": ROUTES.FOLDER_PERMISSIONS,

  // DMS Settings
  "Privileges Settings": ROUTES.PRIVILEGES,
  "Staff Settings": ROUTES.STAFF,
  "Dashboard Settings": ROUTES.DASHBOARD_SETTINGS,
  "AutoFiling Settings": ROUTES.AUTOFILL,

  // Shared Settings
  "Client Branding": ROUTES.BRANDING,
  "Email Format Settings": ROUTES.EMAIL_FORMAT,
  "Audit Logs": ROUTES.AUDIT_LOGS,
};

export const getSectionRoute = (sectionName: string): string | null => {
  return sectionRouteMap[sectionName] || null;
};
