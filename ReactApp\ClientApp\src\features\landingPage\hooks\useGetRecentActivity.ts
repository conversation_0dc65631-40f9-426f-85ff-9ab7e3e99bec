import { useGetRecentActivityQuery } from "@/api/recentActivityApiSlice";
import type { RecentActivityParams } from "@/types/recentActivity";

export const useGetRecentActivity = (params: RecentActivityParams) => {
    const { data: recentActivity,
      isLoading,
      isFetching,
      error,
      refetch: refetchRecentActivity,
    } = useGetRecentActivityQuery(params);

  return {
    recentActivity,
    isLoading,
    isFetching,
    error,
    refetchRecentActivity,
  };
};
