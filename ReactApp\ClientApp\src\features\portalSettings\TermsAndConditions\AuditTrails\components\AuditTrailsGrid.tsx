import {
  Grid,
  GridColumn as Column,
  GridColumnMenuSort,
  type GridPageChangeEvent,
  type GridFilterChangeEvent,
  type GridSortChangeEvent,
} from "@progress/kendo-react-grid";
import { Loader } from "@progress/kendo-react-indicators";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";

import CustomPagination from "./CustomPagination";
import ExportCsvCell from "./ExportCsvCell";
import { auditTrailFilterCellMap } from "./FilterCellMap";
import FormattedDateCell from "./FormattedDateCell";

import type { AuditTrailsColumn, AuditTrail } from "@/types/auditTrails";

interface Props {
  auditTrails: AuditTrail[];
  totalRecordCount: number;
  auditTrailsColumns: AuditTrailsColumn[] | undefined;
  isLoading: boolean;
  isColumnsLoading: boolean;
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
  onPageChange: (_event: GridPageChangeEvent) => void;
  onFilterChange: (_event: GridFilterChangeEvent) => void;
  onSortChange: (_event: GridSortChangeEvent) => void;
  onRefresh: () => void;
}

export default function AuditTrailsGrid({
  auditTrails,
  totalRecordCount,
  auditTrailsColumns,
  isLoading,
  isColumnsLoading,
  skip,
  take,
  filters,
  sorts,
  onPageChange,
  onFilterChange,
  onSortChange,
  onRefresh,
}: Props) {
  if (isColumnsLoading) {
    return <Loader type="infinite-spinner" />;
  }

  return (
    <Grid
      data={auditTrails}
      total={totalRecordCount}
      dataItemKey="id"
      pageable
      sortable
      filterable
      skip={skip}
      take={take}
      filter={filters}
      sort={sorts}
      onPageChange={onPageChange}
      onFilterChange={onFilterChange}
      onSortChange={onSortChange}
      showLoader={isLoading || isColumnsLoading}
      pager={(props) => (
        <CustomPagination
          {...props}
          total={totalRecordCount}
          onRefresh={onRefresh}
          onPageChange={({ page }) =>
            onPageChange({ page } as GridPageChangeEvent)
          }
        />
      )}
    >
      {auditTrailsColumns
        ?.filter((col) => col.key !== "acceptanceHistory")
        .map((column) => (
          <Column
            key={column.key}
            field={column.key}
            title={column.displayValue}
            editor="text"
            filter={column.filterType === "search" ? "text" : "date"}
            sortable={column.sortable}
            filterable={column.filterType !== "none"}
            columnMenu={(props) => <GridColumnMenuSort {...props} />}
            cells={{
              filterCell: auditTrailFilterCellMap[column.key],
              ...(column.key === "uploadedOn" && { data: FormattedDateCell }),
            }}
          />
        ))}

      <Column
        field="acceptanceHistory"
        title="Acceptance History"
        cells={{ data: ExportCsvCell }}
        sortable={false}
        filterable={false}
        width="150px"
      />
    </Grid>
  );
}
