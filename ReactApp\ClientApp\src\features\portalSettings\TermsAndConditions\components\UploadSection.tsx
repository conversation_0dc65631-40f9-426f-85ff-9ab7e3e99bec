import { Upload } from "@progress/kendo-react-upload";
import { Button } from "@progress/kendo-react-buttons";
import { Input } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";

interface Props {
  uploadedFileName: string;
  onUpload: (_e: any) => void;
  onRemove: () => void;
  fileNameFromTerms?: string;
}

export default function UploadSection({
  uploadedFileName,
  onUpload,
  onRemove,
  fileNameFromTerms,
}: Props) {
  const { t } = useTranslation("dashboard");

  const fileNameToShow = uploadedFileName || fileNameFromTerms || "";

  return (
    <div className="upload-section">
      <label className="upload-label">{t("upload.label")}</label>
      <div className="upload-input-row">
        <Input
          value={fileNameToShow}
          placeholder={t("upload.placeholder")}
          readOnly
          className="upload-input"
        />
        {uploadedFileName && (
          <Button icon="close" fillMode="flat" onClick={onRemove} />
        )}
      </div>
      <Upload
        batch={false}
        multiple={false}
        withCredentials={false}
        defaultFiles={[]}
        restrictions={{
          allowedExtensions: [".pdf"],
          maxFileSize: 10485760,
        }}
        onAdd={onUpload}
      />
      <div className="upload-helper-text">{t("upload.helperText")}</div>
    </div>
  );
}
