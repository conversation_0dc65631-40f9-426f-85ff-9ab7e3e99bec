import { render, screen, fireEvent } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { vi } from "vitest";
import LandingPage from "./LandingPage";

// Mock dependencies
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock("@/components/ui/AlertBox/AlertBox", () => ({
  default: ({ message, onClose }: { message: string; onClose: () => void }) => (
    <div data-testid="alert-box">
      <span>{message}</span>
      <button onClick={onClose} data-testid="alert-close">Close</button>
    </div>
  ),
}));

vi.mock("@/components/dashboardLayout/SectionLayout/SectionLayout", () => ({
  default: ({ children, isLoading, isFetching }: { 
    children: React.ReactNode; 
    isLoading?: boolean; 
    isFetching?: boolean; 
  }) => (
    <div data-testid="section-layout" data-loading={isLoading} data-fetching={isFetching}>
      {children}
    </div>
  ),
}));

vi.mock("./components/RecentActivityTable", () => ({
  RecentActivityTable: ({
    data,
    totalRecords,
    dataState,
    isLoading,
    isFetching,
    onRefresh,
    onDataStateChange
  }: {
    data: any[];
    totalRecords?: number;
    dataState?: any;
    isLoading?: boolean;
    isFetching?: boolean;
    onRefresh?: () => void;
    onDataStateChange?: () => void;
  }) => (
    <div
      data-testid="recent-activity-table"
      data-loading={isLoading}
      data-fetching={isFetching}
      data-total={totalRecords}
    >
      <span>Data count: {data.length}</span>
      <button onClick={onRefresh} data-testid="refresh-button">Refresh</button>
      <button onClick={onDataStateChange} data-testid="data-state-change-button">Change State</button>
    </div>
  ),
}));

// Mock the useDashboard hook
const mockUseDashboard = vi.fn();
vi.mock("./hooks/useDashboardController", () => ({
  useDashboard: () => mockUseDashboard(),
}));

const renderLandingPage = () => {
  return render(
    <MemoryRouter>
      <LandingPage />
    </MemoryRouter>
  );
};

describe("LandingPage", () => {
  beforeEach(() => {
    mockUseDashboard.mockClear();
  });

  it("renders SectionLayout with correct loading states", () => {
    mockUseDashboard.mockReturnValue({
      showAlert: false,
      setShowAlert: vi.fn(),
      recentActivityData: [],
      totalRecords: 0,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: true,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    const sectionLayout = screen.getByTestId("section-layout");
    expect(sectionLayout).toBeInTheDocument();
    expect(sectionLayout).toHaveAttribute("data-loading", "true");
    expect(sectionLayout).toHaveAttribute("data-fetching", "false");
  });

  it("renders SectionLayout with fetching state", () => {
    mockUseDashboard.mockReturnValue({
      showAlert: false,
      setShowAlert: vi.fn(),
      recentActivityData: [],
      totalRecords: 0,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: false,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: true,
    });

    renderLandingPage();

    const sectionLayout = screen.getByTestId("section-layout");
    expect(sectionLayout).toHaveAttribute("data-loading", "false");
    expect(sectionLayout).toHaveAttribute("data-fetching", "true");
  });

  it("shows AlertBox when showAlert is true", () => {
    const mockSetShowAlert = vi.fn();
    mockUseDashboard.mockReturnValue({
      showAlert: true,
      setShowAlert: mockSetShowAlert,
      recentActivityData: [],
      totalRecords: 0,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: false,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    expect(screen.getByTestId("alert-box")).toBeInTheDocument();
    expect(screen.getByText("landingPage.alertMessage")).toBeInTheDocument();
  });

  it("hides AlertBox when showAlert is false", () => {
    mockUseDashboard.mockReturnValue({
      showAlert: false,
      setShowAlert: vi.fn(),
      recentActivityData: [],
      totalRecords: 0,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: false,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    expect(screen.queryByTestId("alert-box")).not.toBeInTheDocument();
  });

  it("calls setShowAlert when AlertBox close button is clicked", () => {
    const mockSetShowAlert = vi.fn();
    mockUseDashboard.mockReturnValue({
      showAlert: true,
      setShowAlert: mockSetShowAlert,
      recentActivityData: [],
      totalRecords: 0,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: false,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    fireEvent.click(screen.getByTestId("alert-close"));
    expect(mockSetShowAlert).toHaveBeenCalledWith(false);
  });

  it("shows RecentActivityTable when data exists", () => {
    const mockData = [
      { id: "1", setupArea: "DMS Settings", section: "Test", updatedBy: "User", lastUpdated: "2025-01-01" }
    ];

    mockUseDashboard.mockReturnValue({
      showAlert: false,
      setShowAlert: vi.fn(),
      recentActivityData: mockData,
      totalRecords: 1,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: false,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    expect(screen.getByTestId("recent-activity-table")).toBeInTheDocument();
    expect(screen.getByText("Data count: 1")).toBeInTheDocument();
  });

  it("hides RecentActivityTable when no data exists", () => {
    mockUseDashboard.mockReturnValue({
      showAlert: false,
      setShowAlert: vi.fn(),
      recentActivityData: [],
      totalRecords: 0,
      dataState: { skip: 0, take: 20, sort: [] },
      isLoadingRecentActivity: false,
      handleRefreshRecentActivity: vi.fn(),
      handleDataStateChange: vi.fn(),
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    expect(screen.queryByTestId("recent-activity-table")).not.toBeInTheDocument();
  });

  it("passes correct props to RecentActivityTable", () => {
    const mockData = [
      { id: "1", setupArea: "DMS Settings", section: "Test", updatedBy: "User", lastUpdated: "2025-01-01" }
    ];
    const mockRefresh = vi.fn();
    const mockDataStateChange = vi.fn();

    mockUseDashboard.mockReturnValue({
      showAlert: false,
      setShowAlert: vi.fn(),
      recentActivityData: mockData,
      totalRecords: 1,
      dataState: {
        skip: 0,
        take: 20,
        sort: [{ field: "lastUpdated", dir: "desc" }]
      },
      isLoadingRecentActivity: true,
      handleRefreshRecentActivity: mockRefresh,
      handleDataStateChange: mockDataStateChange,
      isFetchingRecentActivity: false,
    });

    renderLandingPage();

    const table = screen.getByTestId("recent-activity-table");
    expect(table).toHaveAttribute("data-loading", "true");

    fireEvent.click(screen.getByTestId("refresh-button"));
    expect(mockRefresh).toHaveBeenCalled();
  });
});
