import type { RouteObject } from "react-router-dom";
import { ROUTES } from "@/constants/routes";
import DashboardLayout from "@/layouts/DashboardLayout";
import RequireAuth from "@/wrappers/RequireAuth";
import React, { Suspense } from "react";

const LandingPage = React.lazy(
  () => import("@/features/landingPage/LandingPage"),
);
const TermsAndConditions = React.lazy(
  () =>
    import("@/features/portalSettings/TermsAndConditions/TermsAndConditions"),
);
const AuditTrails = React.lazy(
  () =>
    import(
      "@/features/portalSettings/TermsAndConditions/AuditTrails/AuditTrails"
    ),
);
const ErrorPage = React.lazy(() => import("@/features/error/ErrorPage"));

const SampleFeature = React.lazy(
  () => import("@/features/featureTemplate/SampleFeaturePage"),
);

const isLocalhost =
  typeof window !== "undefined" && window.location.hostname === "localhost";

export const dashboardRoutes: RouteObject[] = [
  {
    path: ROUTES.DASHBOARD,
    element: (
      <RequireAuth>
        <DashboardLayout />
      </RequireAuth>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<div>Loading Landing Page...</div>}>
            <LandingPage />
          </Suspense>
        ),
      },
      {
        path: ROUTES.TERMS_AND_CONDITIONS_SLUG,
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<div>Loading Terms And Conditions...</div>}>
                <TermsAndConditions />
              </Suspense>
            ),
          },
          {
            path: ROUTES.AUDIT_TRAILS_SLUG,
            element: (
              <Suspense fallback={<div>Loading Audit Trails...</div>}>
                <AuditTrails />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: ROUTES.ERROR,
        element: (
          <Suspense fallback={<div>Loading Error...</div>}>
            <ErrorPage />
          </Suspense>
        ),
      },
      // Conditionally add SampleFeature route only in localhost
      ...(isLocalhost
        ? [
            {
              path: ROUTES.SAMPLE_FEATURE,
              element: (
                <Suspense fallback={<div>Loading Sample Feature...</div>}>
                  <SampleFeature />
                </Suspense>
              ),
            },
          ]
        : []),
    ],
  },
];
